/**
 * Building a Parser from scratch
 *
 * Course info: http://dmitrysoshnikov.com/courses/parser-from-scratch/
 *
 * (C) 2020-present <PERSON> <<EMAIL>>
 */

/**
 * Class declaration.
 */
class Point {
  fn constructor(x, y) {
    this.x = x;
    this.y = y;
  }

  fn calc() {
    return this.x + this.y;
  }
}

/**
 * Child class.
 */
class Point3D extends Point {
  fn constructor(x, y, z) {
    super(x, y);
    this.z = z;
  }

  fn calc() {
    return super() + this.z;
  }
}

// Instance:
let p = new Point3D(10, 20, 30);

p.calc();
