#!/usr/bin/env deno --allow-env --allow-read

/**
 * Building a Parser from scratch
 *
 * Course info: http://dmitrysoshnikov.com/courses/parser-from-scratch/
 *
 * (C) 2020-present <PERSON> <<EMAIL>>
 */

'use strict';

import { Parser } from '../src/Parser.ts';
import { toASTString } from '../src/codegen/ASTStringGen.ts';

import { readFileSync } from 'node:fs';

function main(argv) {
  const [_node, _path, mode, exp] = argv;

  const parser = new Parser();

  let ast = null;

  // Direct expression:

  if (mode === '-e') {
    ast = parser.parse(exp);
  }

  if (mode === '-f') {
    const src = readFileSync(exp, 'utf-8');
    ast = parser.parse(src);
  }

  console.log(toASTString(ast));
}

main(process.argv);
