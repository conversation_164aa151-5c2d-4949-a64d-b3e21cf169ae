import {
  ASTNode,
  BlockStatement,
  CallExpression,
  FunctionDeclaration,
  Identifier,
  MemberExpression,
  Program,
} from "./AST.ts";
import { ASTVisitor } from "./ASTVisitor.ts";
import { <PERSON><PERSON><PERSON><PERSON> } from "../playground/typecheck.ts";
import { createMangledFunctionName } from "./utils/TypeNameUtils.ts";

interface CallSite {
  argTypes: string[];
  returnType: string;
  callNode: CallExpression;
}

export class Monomorphizer {
  private monomorphizedFunctions = new Map<string, FunctionDeclaration>();
  private originalFunctions = new Map<string, FunctionDeclaration>();
  private callSites = new Map<string, CallSite[]>();
  private typeChecker: TypeChecker; // Add reference to type checker

  constructor(typeChecker: TypeChecker) {
    this.typeChecker = typeChecker;
  }

  convert<T extends ASTNode>(node: T): T {
    // First pass: collect all function declarations
    this.collectFunctions(node);

    // Second pass: transform the AST and create monomorphized versions
    const transformedAST = ASTVisitor.transform(node, (node) => {
      if (node.type === "CallExpression") {
        return this.transformCallExpression(node);
      }
      return node;
    });

    // Third pass: remove original generic functions
    const cleanedAST = ASTVisitor.transform(transformedAST, (node) => {
      return this.removeOriginalFunctionsFromNode(node);
    });

    // Fourth pass: add monomorphized functions to the program
    this.addMonomorphizedFunctionsToProgram(cleanedAST);

    return cleanedAST;
  }

  private addMonomorphizedFunctionsToProgram(ast: ASTNode): void {
    if (ast.type === "Program") {
      const program = ast as Program;
      const monomorphizedFunctions = Array.from(
        this.monomorphizedFunctions.values()
      );

      // Find the index where builtins end (first non-builtin function)
      const insertIndex = program.body.findIndex(
        (stmt) => stmt.type === "FunctionDeclaration" && !stmt.isBuiltin
      );

      if (insertIndex === -1) {
        // No non-builtin functions found, add at the beginning
        program.body.unshift(...monomorphizedFunctions);
      } else {
        // Insert before the first non-builtin function
        program.body.splice(insertIndex, 0, ...monomorphizedFunctions);
      }
    } else {
      // Handle nested programs if needed
      ASTVisitor.walkAST(ast, (node) => {
        if (node.type === "Program") {
          this.addMonomorphizedFunctionsToProgram(node);
        }
      });
    }
  }

  private collectFunctions(ast: ASTNode): void {
    // Use findNodes to collect all function declarations at once
    const functions = ASTVisitor.findNodes<FunctionDeclaration>(
      ast,
      "FunctionDeclaration"
    );

    for (const func of functions) {
      this.originalFunctions.set(func.name.name, func);
    }
  }

  private transformCallExpression(node: CallExpression): CallExpression {
    if (node.callee.type === "Identifier") {
      const functionName = node.callee.name;

      if (this.originalFunctions.has(functionName)) {
        // First, recursively transform all arguments to ensure nested calls are processed
        // This is crucial for getting correct types from nested calls
        node.args = node.args.map((arg) => {
          if (arg.type === "CallExpression") {
            return this.transformCallExpression(arg);
          }
          return arg;
        });

        // Now extract types after arguments have been transformed
        const argTypes = this.extractArgTypes(node);
        const returnType = this.extractReturnType(node, argTypes); // Pass argTypes directly
        const monomorphicName = this.getMonomorphicName(functionName, argTypes);

        if (!this.monomorphizedFunctions.has(monomorphicName)) {
          this.createMonomorphizedFunction(functionName, argTypes, returnType);
        }

        // Update the call to use the monomorphized function
        node.callee.name = monomorphicName;

        // Set the inferred type on this call expression
        node.inferredType = returnType;

        // node.args.map((arg) => arg.inferredType = argTypes[0]);

        this.trackCallSite(functionName, argTypes, returnType, node);
      }
    }

    return node;
  }

  private removeOriginalFunctionsFromNode<T extends ASTNode>(node: T): T {
    if (node.type === "Program" || node.type === "BlockStatement") {
      const block = node as Program | BlockStatement;

      // Filter out original function declarations that were monomorphized
      block.body = block.body.filter((stmt) => {
        if (stmt.type === "FunctionDeclaration") {
          const funcName = stmt.name.name;
          return (
            !this.originalFunctions.has(funcName) ||
            !this.callSites.has(funcName)
          );
        }
        return true;
      });
    }

    return node;
  }

  // private createMonomorphizedFunction(
  //   originalName: string,
  //   argTypes: string[],
  //   returnType: string,
  // ): void {
  //   const originalFunc = this.originalFunctions.get(originalName)!;
  //   const monomorphicName = this.getMonomorphicName(originalName, argTypes);
  //
  //   // Deep clone the original function
  //   const monomorphizedFunc = new FunctionDeclaration(
  //     new Identifier(monomorphicName),
  //     originalFunc.params.map((param, i) => ({
  //       type: "Identifier",
  //       name: param.name,
  //       inferredType: argTypes[i],
  //     })),
  //     this.cloneASTNode(originalFunc.body),
  //   );
  //   monomorphizedFunc.inferredType = returnType;
  //
  //   // Transform the body using ASTVisitor to handle nested calls
  //   monomorphizedFunc.body = ASTVisitor.transform(
  //     monomorphizedFunc.body,
  //     (node) => {
  //       if (node.type === "CallExpression") {
  //         return this.transformCallExpression(node);
  //       }
  //       return node;
  //     },
  //   );
  //
  //   this.monomorphizedFunctions.set(monomorphicName, monomorphizedFunc);
  // }

  private createMonomorphizedFunction(
    originalName: string,
    argTypes: string[],
    returnType: string
  ): void {
    const originalFunc = this.originalFunctions.get(originalName)!;
    const monomorphicName = this.getMonomorphicName(originalName, argTypes);

    // Deep clone the original function
    const monomorphizedFunc: FunctionDeclaration = {
      type: "FunctionDeclaration",
      name: {
        type: "Identifier",
        name: monomorphicName,
      },
      params: originalFunc.params.map((param, i) => ({
        type: "Identifier",
        name: param.name,
        inferredType: argTypes[i],
      })),
      body: this.cloneASTNode(originalFunc.body),
      inferredType: returnType,
    };

    // Set type information on the cloned body
    this.setTypeInformationOnClonedBody(
      monomorphizedFunc.body,
      argTypes,
      returnType
    );

    // Transform the body recursively to handle nested calls
    monomorphizedFunc.body = ASTVisitor.transform(
      monomorphizedFunc.body,
      (node) => {
        if (node.type === "CallExpression") {
          return this.transformCallExpression(node);
        }
        return node;
      }
    ) as BlockStatement;

    this.monomorphizedFunctions.set(monomorphicName, monomorphizedFunc);
  }

  private setTypeInformationOnClonedBody(
    body: BlockStatement,
    _argTypes: string[],
    returnType: string
  ): void {
    ASTVisitor.walkAST(body, (node) => {
      if (node.type === "ReturnStatement") {
        // Set the return type on return statements
        node.inferredType = returnType;

        // If there's a return argument, it should also have the return type
        if (node.argument) {
          node.argument.inferredType = returnType;
        }
      }
      // You can add more type setting logic here for other node types
    });
  }

  // All the helper methods remain the same
  private extractArgTypes(node: CallExpression): string[] {
    return node.args.map((arg) => {
      // If it's a call expression that has been transformed, use its inferred type
      if (arg.type === "CallExpression" && arg.inferredType) {
        return this.normalizeTypeName(arg.inferredType);
      }

      // Otherwise, extract type from the original typed AST
      const type = arg.inferredType;
      return this.normalizeTypeName(type);
    });
  }

  private extractReturnType(node: CallExpression, argTypes: string[]): string {
    if (node.callee.type === "Identifier") {
      const functionName = node.callee.name;
      const originalFunc = this.originalFunctions.get(functionName);

      if (originalFunc) {
        // Type-check the function with specific argument types
        return this.typeCheckFunctionWithArgs(originalFunc, argTypes);
      }
    }

    const type = node.inferredType;
    return this.normalizeTypeName(type);
  }

  private typeCheckFunctionWithArgs(
    func: FunctionDeclaration,
    argTypes: string[]
  ): string {
    // Create a new scope with the specific parameter types
    const savedScope = this.typeChecker.saveScope();

    // Bind parameters to their concrete types
    for (let i = 0; i < func.params.length && i < argTypes.length; i++) {
      const paramName = func.params[i].name;
      const typeId = this.typeChecker.createConcreteType(argTypes[i]);
      this.typeChecker.setVariableType(paramName, typeId);
    }

    // Check for invalid field accesses before type-checking
    this.validateFieldAccesses(func, argTypes);

    // Type-check the function body with these concrete types
    const returnTypeId = this.typeChecker.visitNode(func.body);
    const returnTypeName = this.typeChecker.getConcreteTypeName(returnTypeId);

    // Restore the original scope
    this.typeChecker.restoreScope(savedScope);

    return returnTypeName || "unknown";
  }

  private validateFieldAccesses(
    func: FunctionDeclaration,
    argTypes: string[]
  ): void {
    // Find all member expressions in the function body
    const memberExpressions: MemberExpression[] = [];

    ASTVisitor.walkAST(func.body, (node) => {
      if (node.type === "MemberExpression") {
        memberExpressions.push(node);
      }
    });

    // Check each member expression
    for (const memberExpr of memberExpressions) {
      if (memberExpr.object?.type === "Identifier" && !memberExpr.computed) {
        const paramName = (memberExpr.object as Identifier).name;
        const fieldName = (memberExpr.property as Identifier)?.name;

        if (fieldName) {
          // Find which parameter this refers to
          const paramIndex = func.params.findIndex((p) => p.name === paramName);
          if (paramIndex !== -1 && paramIndex < argTypes.length) {
            const argType = argTypes[paramIndex];

            // Check if this is a struct type and if the field exists
            if (argType.startsWith("{") && argType.endsWith("}")) {
              const hasField = this.structTypeHasField(argType, fieldName);
              if (!hasField) {
                console.warn(
                  `⚠️  Warning: Function '${func.name.name}' accesses field '${fieldName}' on parameter '${paramName}', ` +
                    `but the argument type '${argType}' does not have this field.`
                );
              }
            }
          }
        }
      }
    }
  }

  private structTypeHasField(structType: string, fieldName: string): boolean {
    // Parse "{ a: String, b: Number, c: Number }" to check if fieldName exists
    const inner = structType.slice(1, -1).trim();
    if (inner === "") return false;

    const fields = inner.split(",");
    for (const field of fields) {
      const trimmed = field.trim();
      const colonIndex = trimmed.indexOf(":");
      if (colonIndex !== -1) {
        const existingFieldName = trimmed.substring(0, colonIndex).trim();
        if (existingFieldName === fieldName) {
          return true;
        }
      }
    }

    return false;
  }

  private normalizeTypeName(type: unknown): string {
    if (typeof type === "string") {
      return type;
    }
    if (type && typeof type === "object" && "concrete" in type) {
      return type.concrete as string;
    }
    return "unknown";
  }

  private getMonomorphicName(baseName: string, argTypes: string[]): string {
    return createMangledFunctionName(baseName, argTypes);
  }

  private trackCallSite(
    functionName: string,
    argTypes: string[],
    returnType: string,
    callNode: CallExpression
  ): void {
    if (!this.callSites.has(functionName)) {
      this.callSites.set(functionName, []);
    }

    this.callSites.get(functionName)!.push({
      argTypes,
      returnType,
      callNode,
    });
  }

  private cloneASTNode<T extends ASTNode>(node: T): T {
    return JSON.parse(JSON.stringify(node));
  }

  // Utility methods remain the same
  getMonomorphizationSummary(): string {
    let summary = "Monomorphization Summary:\n";

    for (const [originalName, callSites] of this.callSites) {
      summary += `\nFunction: ${originalName}\n`;

      const uniqueSignatures = new Set<string>();
      for (const site of callSites) {
        const signature = `${site.argTypes.join(", ")} -> ${site.returnType}`;
        uniqueSignatures.add(signature);
      }

      for (const signature of uniqueSignatures) {
        const argTypes = signature.split(" -> ")[0].split(", ");
        const monomorphicName = this.getMonomorphicName(originalName, argTypes);
        summary += `  ${monomorphicName}: (${signature})\n`;
      }
    }

    return summary;
  }

  // getMonomorphizedOriginalNames(): Set<string> {
  //   return new Set(this.callSites.keys());
  // }

  // getMonomorphizedFunctionNames(): string[] {
  //   return Array.from(this.monomorphizedFunctions.keys());
  // }
}
