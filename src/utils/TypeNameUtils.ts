/**
 * Utility functions for converting type names into valid identifiers
 */

/**
 * Converts a type name into a valid identifier by replacing invalid characters
 * Examples:
 * - "{ a: String, b: Number }" -> "struct_a_String_b_Number"
 * - "String" -> "String"
 * - "Function(2)" -> "Function2"
 * - "Array<String>" -> "Array_String"
 */
export function typeToValidIdentifier(typeName: string): string {
  // Handle struct types like "{ a: String, b: Number, c: Number }"
  if (typeName.startsWith("{") && typeName.endsWith("}")) {
    // Extract field definitions from inside braces
    const inner = typeName.slice(1, -1).trim();
    if (inner === "") return "struct_empty";
    
    // Split by comma and process each field
    const fields = inner.split(",").map((field) => {
      const trimmed = field.trim();
      // Handle "fieldName: TypeName" format
      const colonIndex = trimmed.indexOf(":");
      if (colonIndex !== -1) {
        const fieldName = trimmed.substring(0, colonIndex).trim();
        const fieldType = trimmed.substring(colonIndex + 1).trim();
        return `${fieldName}_${typeToValidIdentifier(fieldType)}`;
      }
      return trimmed.replace(/[^a-zA-Z0-9_]/g, "_");
    });
    
    return `struct_${fields.join("_")}`;
  }
  
  // Handle function types like "Function(2)"
  if (typeName.startsWith("Function(") && typeName.endsWith(")")) {
    const paramCount = typeName.slice(9, -1); // Extract number between parentheses
    return `Function${paramCount}`;
  }
  
  // Handle generic types like "Array<String>" or "Map<String, Number>"
  if (typeName.includes("<") && typeName.includes(">")) {
    const openIndex = typeName.indexOf("<");
    const closeIndex = typeName.lastIndexOf(">");
    
    if (openIndex !== -1 && closeIndex !== -1 && closeIndex > openIndex) {
      const baseType = typeName.substring(0, openIndex);
      const genericParams = typeName.substring(openIndex + 1, closeIndex);
      
      // Process generic parameters recursively
      const processedParams = genericParams
        .split(",")
        .map(param => typeToValidIdentifier(param.trim()))
        .join("_");
      
      return `${typeToValidIdentifier(baseType)}_${processedParams}`;
    }
  }
  
  // Handle other special characters - replace with underscores
  let result = typeName.replace(/[^a-zA-Z0-9_]/g, "_");
  
  // Remove consecutive underscores
  result = result.replace(/_+/g, "_");
  
  // Remove leading/trailing underscores
  result = result.replace(/^_+|_+$/g, "");
  
  // Ensure it starts with a letter or underscore (valid identifier start)
  if (result.length > 0 && /^[0-9]/.test(result)) {
    result = "_" + result;
  }
  
  // Handle empty result
  if (result === "") {
    result = "unknown";
  }
  
  return result;
}

/**
 * Creates a mangled function name from a base name and argument types
 * Ensures the result is a valid identifier
 */
export function createMangledFunctionName(baseName: string, argTypes: string[]): string {
  if (argTypes.length === 0) return baseName;
  
  const typeSignature = argTypes
    .map(type => typeToValidIdentifier(type))
    .join("_");
    
  return `${baseName}_${typeSignature}`;
}

/**
 * Normalizes a type name to a consistent format
 * Useful for comparing types or creating consistent identifiers
 */
export function normalizeTypeName(typeName: string): string {
  // Remove extra whitespace
  let normalized = typeName.replace(/\s+/g, " ").trim();
  
  // Normalize struct type formatting
  if (normalized.startsWith("{") && normalized.endsWith("}")) {
    const inner = normalized.slice(1, -1).trim();
    if (inner === "") return "{}";
    
    // Normalize field formatting: "field:Type" -> "field: Type"
    const fields = inner.split(",").map(field => {
      const trimmed = field.trim();
      const colonIndex = trimmed.indexOf(":");
      if (colonIndex !== -1) {
        const fieldName = trimmed.substring(0, colonIndex).trim();
        const fieldType = trimmed.substring(colonIndex + 1).trim();
        return `${fieldName}: ${fieldType}`;
      }
      return trimmed;
    });
    
    return `{ ${fields.join(", ")} }`;
  }
  
  return normalized;
}
