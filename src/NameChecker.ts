import {
  AssignmentExpression,
  BinaryExpression,
  BlockStatement,
  CallExpression,
  ClassDeclaration,
  Expression,
  ExpressionStatement,
  FunctionDeclaration,
  FunctionExpression,
  Identifier,
  MemberExpression,
  ReturnStatement,
  Statement,
  StructLiteral,
  SuperExpression,
  ThisExpression,
  VariableStatement,
} from "./AST.ts";

interface NamingError {
  message: string;
  node: Expression | Statement;
}

export class NameChecker {
  scopes = Array<Set<string>>();
  errors = Array<NamingError>();

  private classStack = Array<ClassDeclaration>();
  private inMethod = false;

  reportError(message: string, node: Expression | Statement) {
    this.errors.push({ message, node });
  }

  visitNode(node: Expression | Statement) {
    switch (node.type) {
      // Statements

      case "BlockStatement":
        this.visitBlockStatement(node);
        break;

      case "VariableStatement":
        this.visitVariableStatement(node);
        break;

      case "ExpressionStatement":
        this.visitExpressionStatement(node);
        break;

      case "ReturnStatement":
        this.visitReturnStatement(node);
        break;

      case "FunctionDeclaration":
        this.visitFunctionDeclaration(node);
        break;

      case "ClassDeclaration":
        this.visitClassDeclaration(node);
        break;

      // Expressions

      case "Identifier":
        this.visitIdentifier(node);
        break;

      case "AssignmentExpression":
        this.visitAssignmentExpression(node);
        break;

      case "BinaryExpression":
        this.visitBinaryExpression(node);
        break;

      case "FunctionExpression":
        this.visitFunctionExpression(node);
        break;

      case "CallExpression":
        this.visitCallExpression(node);
        break;

      case "ThisExpression":
        this.visitThisExpression(node);
        break;

      case "SuperExpression":
        this.visitSuperExpression(node);
        break;

      case "NewExpression":
        break;

      case "MemberExpression":
        this.visitMemberExpression(node);
        break;

      case "StructLiteral":
        this.visitStructLiteral(node);
        break;

      // Literals don't need name resolution
      case "StringLiteral":
      case "NumericLiteral":
      case "BooleanLiteral":
        break;
      default:
        throw new Error(`Unknown node type: ${node.type}`);
    }
  }

  declareVariable(name: string, node: Expression | Statement) {
    const currentScope = this.scopes.at(-1);

    if (currentScope?.has(name)) {
      this.reportError(`Duplicate declaration of variable: ${name}`, node);
      return false;
    }

    currentScope?.add(name);
    return true;
  }

  // Statements

  visitExpressionStatement(node: ExpressionStatement) {
    this.visitNode(node.expression);
  }

  visitVariableStatement(node: VariableStatement) {
    for (const declaration of node.declarations) {
      if (declaration.init) this.visitNode(declaration.init);
      if (!this.declareVariable(declaration.id.name, node)) {
        return;
      }
    }
  }

  visitBlockStatement(node: BlockStatement) {
    for (const statement of node.body) {
      this.visitNode(statement);
    }
  }

  visitClassDeclaration(node: ClassDeclaration) {
    // Visit superclass if present
    if (node.superClass) {
      this.visitNode(node.superClass);
    }

    // Declare the class name in current scope
    this.declareVariable(node.id.name, node);

    // Push class context
    this.classStack.push(node);
    this.scopes.push(new Set());

    this.visitNode(node.body);

    // Pop contexts
    this.scopes.pop();
    this.classStack.pop();
  }

  visitReturnStatement(node: ReturnStatement) {
    if (!node.argument) return;
    this.visitNode(node.argument);
  }

  visitFunctionDeclaration(node: FunctionDeclaration) {
    // Check if this is a method (function inside a class)
    const wasInMethod = this.inMethod;
    this.inMethod = this.classStack.length > 0;

    // For standalone functions, declare the name in current scope
    if (!this.inMethod) {
      this.declareVariable(node.name.name, node);
    }

    // Create new scope for function parameters and body
    this.scopes.push(new Set());

    // Add parameters to function scope
    for (const param of node.params) {
      this.declareVariable(param.name, param);
    }

    this.visitNode(node.body);

    // Pop scope and restore method context
    this.scopes.pop();
    this.inMethod = wasInMethod;
  }

  // Expressions

  visitIdentifier(node: Identifier) {
    if (!this.scopes.some((scope) => scope.has(node.name))) {
      this.reportError(`Reference to undeclared variable: ${node.name}`, node);
    }
  }

  visitStructLiteral(node: StructLiteral) {
    for (const field of node.fields) {
      this.visitNode(field.value);
    }
  }

  visitBinaryExpression(node: BinaryExpression) {
    this.visitNode(node.left);
    this.visitNode(node.right);
  }

  visitFunctionExpression(node: FunctionExpression) {
    // Create new scope for function parameters and body
    this.scopes.push(new Set());

    for (const param of node.params) {
      this.declareVariable(param.name, param);
    }

    this.visitNode(node.body);

    // Pop scope and restore method context
    this.scopes.pop();
  }

  visitCallExpression(node: CallExpression) {
    this.visitNode(node.callee);

    for (const arg of node.args) {
      this.visitNode(arg);
    }
  }

  visitAssignmentExpression(node: AssignmentExpression) {
    this.visitNode(node.left);
    this.visitNode(node.right);
  }

  visitMemberExpression(node: MemberExpression) {
    // Visit the object part (left side of the dot)
    this.visitNode(node.object);

    // For computed member access (obj[prop]), visit the property
    if (node.computed) {
      this.visitNode(node.property);
    }

    // For non-computed access (obj.prop), the property is just an identifier
    // that doesn't need resolution - it's a property name, not a variable reference
  }

  visitThisExpression(node: ThisExpression) {
    // 'this' can only be used within class methods
    if (!this.inMethod) {
      this.reportError("'this' can only be used within class methods", node);
      return;
    }

    // 'this' is valid in this context
  }

  visitSuperExpression(node: SuperExpression) {
    // 'super' can only be used in classes that extend another class
    const currentClass = this.classStack.at(-1);

    if (!currentClass) {
      this.reportError("'super' can only be used within a class", node);
      return;
    }

    if (!currentClass.superClass) {
      this.reportError(
        "'super' can only be used in classes that extend another class",
        node
      );
      return;
    }

    // Check if we're in a method context
    if (!this.inMethod) {
      this.reportError("'super' can only be used within class methods", node);
      return;
    }
  }

  visitConditionalExpression(node: any) {
    this.visitNode(node.test);
    this.visitNode(node.consequent);
    this.visitNode(node.alternate);
  }

  visitArrayLiteral(node: any) {
    for (const element of node.elements) {
      this.visitNode(element);
    }
  }

  nameCheck(statements: Statement[]) {
    // Reset globals
    this.errors = [];
    this.scopes = [new Set()];

    for (const statement of statements) {
      this.visitNode(statement);
    }

    this.scopes.pop();

    return { errors: this.errors };
  }
}
