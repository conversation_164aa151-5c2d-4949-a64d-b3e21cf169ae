import {
  ASTNode,
  BinaryExpression,
  BlockStatement,
  CallExpression,
  ExpressionStatement,
  FunctionDeclaration,
  Identifier,
  ReturnStatement,
  Statement,
  VariableStatement,
} from "./AST.ts";
import { createMangledFunctionName } from "./utils/TypeNameUtils.ts";

interface NamingError {
  message: string;
  node: ASTNode;
}

type ConcreteType = { concrete: string };
type SymlinkType = { symlink: number };
type FunctionType = { function: { params: number[]; return: number } };

type TypeEntry = ConcreteType | SymlinkType | FunctionType;

export class TypeChecker {
  db = Array<TypeEntry | null>();
  errors = Array<NamingError>();
  nextTypeId = 0;
  scope: Record<string, number> = {}; // Variable scope to track types

  genericFunctions = new Set<string>();

  functionNodes = new Map<string, FunctionDeclaration>();

  // Track all instantiations of generic functions
  functionInstantiations = new Map<
    string,
    Array<{
      paramTypes: number[];
      returnType: number;
      mangledName: string;
    }>
  >();

  freshTypeId() {
    const id = this.nextTypeId++;
    this.db[id] = null;
    return id;
  }

  reportError(message: string, node: ASTNode) {
    this.errors.push({ message, node });
  }

  createConcreteType(typeName: string) {
    const id = this.nextTypeId++;
    this.db[id] = { concrete: typeName };
    return id;
  }

  isConcrete(entry: TypeEntry | null): entry is ConcreteType {
    return entry ? "concrete" in entry : false;
  }

  isSymlink(entry: TypeEntry | null): entry is SymlinkType {
    return entry ? "symlink" in entry : false;
  }

  isFunctionType(entry: TypeEntry | null): entry is FunctionType {
    return entry ? "function" in entry : false;
  }

  resolveSymlinksAndCompress(typeId: number): number {
    const entry = this.db[typeId];

    // If it's null, it's a type variable that hasn't been unified yet
    if (entry === null) {
      return typeId;
    }

    // If it's a symlink, follow it (with path compression)
    if (this.isSymlink(entry)) {
      const ultimateTypeId = this.resolveSymlinksAndCompress(entry.symlink);

      // Path compression: update the symlink to point directly to the ultimate type
      if (ultimateTypeId !== entry.symlink) {
        this.db[typeId] = { symlink: ultimateTypeId };
      }

      return ultimateTypeId;
    }

    // For concrete types
    return typeId;
  }

  getConcreteTypeName(typeId: number) {
    const ultimateId = this.resolveSymlinksAndCompress(typeId);
    const entry = this.db[ultimateId];

    if (this.isConcrete(entry)) {
      return entry.concrete;
    }

    return null;
  }

  reportTypeMismatch(typeId1: number, typeId2: number, node: ASTNode) {
    const type1Name = this.getConcreteTypeName(typeId1) || "unknown";
    const type2Name = this.getConcreteTypeName(typeId2) || "unknown";

    this.reportError(
      `Type mismatch: cannot unify ${type1Name} with ${type2Name}`,
      node
    );
    return false;
  }

  unify(aTypeId: number, bTypeId: number, node: ASTNode): boolean {
    const aType = this.resolveSymlinksAndCompress(aTypeId);
    const bType = this.resolveSymlinksAndCompress(bTypeId);

    // If they're already the same, we're done
    if (aType === bType) return true;

    const aEntry = this.db[aType];
    const bEntry = this.db[bType];

    // Handle function type unification
    if (this.isFunctionType(aEntry) && this.isFunctionType(bEntry)) {
      const aFunc = aEntry.function;
      const bFunc = bEntry.function;

      // Check parameter count
      if (aFunc.params.length !== bFunc.params.length) {
        this.reportError(
          `Function parameter count mismatch: expected ${aFunc.params.length}, got ${bFunc.params.length}`,
          node
        );
        return false;
      }

      // Unify parameter types
      for (let i = 0; i < aFunc.params.length; i++) {
        if (!this.unify(aFunc.params[i], bFunc.params[i], node)) {
          return false;
        }
      }

      // Unify return types
      return this.unify(aFunc.return, bFunc.return, node);
    }

    // If both have concrete types and they're different, report mismatch
    if (
      this.isConcrete(aEntry) &&
      this.isConcrete(bEntry) &&
      aEntry.concrete !== bEntry.concrete
    ) {
      return this.reportTypeMismatch(aType, bType, node);
    }

    if (aEntry === null) {
      // If aEntry is null (unassigned type variable)
      this.db[aType] =
        bEntry === null
          ? { symlink: bType }
          : this.isConcrete(bEntry)
          ? { concrete: bEntry.concrete }
          : { symlink: bType };
      return true;
    } else if (bEntry === null) {
      return this.unify(bTypeId, aTypeId, node); // Swap the args
    } else if (this.isConcrete(aEntry)) {
      // a is concrete, b is not null but must be a variable
      this.db[bType] = { symlink: aType };
    } else {
      // a is not concrete and not null, so must be a variable
      this.db[aType] = { symlink: bType };
    }

    return true;
  }

  visitNode(node: ASTNode): number {
    switch (node.type) {
      // case "ConstDeclaration":
      //   return visitConstDeclaration(node);
      case "ExpressionStatement":
        return this.visitExpressionStatement(node);

      case "VariableStatement":
        return this.visitVariableStatement(node);

      case "FunctionDeclaration":
        return this.visitFunctionDeclaration(node);

      case "Identifier":
        return this.visitIdentifier(node);

      case "ReturnStatement":
        return this.visitReturnStatement(node);

      case "BinaryExpression":
        return this.visitBinaryExpression(node);

      // case "ConditionalExpression":
      //   return visitConditionalExpression(node);

      case "CallExpression":
        return this.visitCallExpression(node);

      // case "ArrayLiteral":
      //   return visitArrayLiteral(node);

      case "BlockStatement":
        return this.visitBlockStatement(node);

      case "StringLiteral":
        return this.createConcreteType("String");

      case "NumericLiteral":
        return this.createConcreteType("Number");

      case "BooleanLiteral":
        return this.createConcreteType("Boolean");

      default:
        this.reportError(
          `Unknown node type during type checking: ${node.type}`,
          node
        );
        return this.freshTypeId(); // Return a fresh type as a fallback
    }
  }

  visitExpressionStatement(node: ExpressionStatement): number {
    return this.visitNode(node.expression);
  }

  visitIdentifier(node: Identifier): number {
    // Look up the variable in the scope
    if (this.scope[node.name] !== undefined) {
      withId(node).typeId = this.scope[node.name];
      return withId(node).typeId;
    }

    // If not found in scope, create a fresh type variable
    if (withId(node).typeId === undefined) {
      withId(node).typeId = this.freshTypeId();
    }
    return withId(node).typeId;
  }

  visitBinaryExpression(node: BinaryExpression): number {
    const leftType = this.visitNode(node.left);
    const rightType = this.visitNode(node.right);

    // Get concrete types for more precise error messages
    const leftConcrete = this.getConcreteTypeName(leftType);
    const rightConcrete = this.getConcreteTypeName(rightType);

    if (node.operator === "+") {
      // Check if we have concrete types and they don't match
      if (leftConcrete && rightConcrete && leftConcrete !== rightConcrete) {
        this.reportError(
          `Type mismatch in binary operation: cannot add ${leftConcrete} to ${rightConcrete}`,
          node
        );
        return this.createConcreteType("Number"); // Return a placeholder type
      }

      // If not both concrete, try to unify
      const canUnify = this.unify(leftType, rightType, node);
      if (!canUnify) {
        this.reportError(
          `Type mismatch in binary operation: cannot add ${
            leftConcrete || "unknown"
          } to ${rightConcrete || "unknown"}`,
          node
        );
        return this.createConcreteType("Number"); // Return a placeholder type
      }

      return leftType;
    } else if (node.operator === "*") {
      // Multiplication: both operands must be numbers
      const numberType = this.createConcreteType("Number");

      // Check if we have concrete types that aren't numbers
      if (leftConcrete && leftConcrete !== "Number") {
        this.reportError(
          `Type mismatch: expected Number for left operand of '*' operator, got ${leftConcrete}`,
          node.left
        );
      }

      if (rightConcrete && rightConcrete !== "Number") {
        this.reportError(
          `Type mismatch: expected Number for right operand of '*' operator, got ${rightConcrete}`,
          node.right
        );
      }

      // If we don't have concrete types, try to unify with Number
      if (!leftConcrete) {
        this.unify(leftType, numberType, node.left);
      }

      if (!rightConcrete) {
        this.unify(rightType, numberType, node.right);
      }

      return numberType;
    }

    // Default case: ensure both operands have the same type
    if (leftConcrete && rightConcrete && leftConcrete !== rightConcrete) {
      this.reportError(
        `Type mismatch in binary operation: operands must have the same type, got ${leftConcrete} and ${rightConcrete}`,
        node
      );
    } else if (!this.unify(leftType, rightType, node)) {
      this.reportError(
        `Type mismatch in binary operation: operands must have the same type`,
        node
      );
    }

    return leftType;
  }

  createFunctionType(paramTypes: number[], returnType: number): number {
    const functionTypeId = this.freshTypeId();

    // Store function signature in the database
    // You'll need to extend TypeEntry to support function types
    this.db[functionTypeId] = {
      function: {
        params: paramTypes,
        return: returnType,
      },
    };

    return functionTypeId;
  }

  // visitFunctionDeclaration(node: FunctionDeclaration): number {
  //   // Create parameter types and add them to a new scope
  //   const paramTypes = [];
  //   const savedScope = { ...this.scope }; // Save current scope
  //
  //   for (const param of node.params) {
  //     const paramType = this.freshTypeId();
  //     withId(param).typeId = paramType;
  //     this.scope[param.name] = paramType; // Add parameter to scope
  //     paramTypes.push(paramType);
  //   }
  //
  //   // Visit function body with parameters in scope
  //   const bodyType = this.visitNode(node.body);
  //
  //   // Create function type that captures parameter and return types
  //   const functionType = this.createFunctionType(paramTypes, bodyType);
  //
  //   // Restore previous scope
  //   this.scope = savedScope;
  //
  //   // Add function to scope with its type
  //   this.scope[node.name.name] = functionType;
  //   withId(node).typeId = functionType;
  //
  //   return functionType;
  // }

  visitFunctionDeclaration(node: FunctionDeclaration): number {
    // Mark this function as generic (no type annotations)
    this.genericFunctions.add(node.name.name);
    this.functionNodes.set(node.name.name, node);

    // Create a placeholder function type for the scope
    // This won't be used for type checking, just for name resolution
    const placeholderType = this.freshTypeId();
    this.scope[node.name.name] = placeholderType;
    withId(node).typeId = placeholderType;

    return placeholderType;
  }

  // visitCallExpression(node: CallExpression): number {
  //   const calleeType = this.visitNode(node.callee);
  //
  //   // Visit arguments to get their types
  //   const argTypes = node.args.map((arg) => this.visitNode(arg));
  //
  //   // Create expected return type
  //   const returnType = this.freshTypeId();
  //
  //   // Create expected function type based on arguments
  //   const expectedFunctionType = this.createFunctionType(argTypes, returnType);
  //
  //   // Unify the callee type with expected function type
  //   if (!this.unify(calleeType, expectedFunctionType, node)) {
  //     this.reportError(
  //       `Function call type mismatch: expected function with ${argTypes.length} parameters`,
  //       node,
  //     );
  //   }
  //
  //   return returnType;
  // }

  visitCallExpression(node: CallExpression): number {
    // Visit arguments first to get their types
    const argTypes = node.args.map((arg) => this.visitNode(arg));

    if (node.callee.type === "Identifier") {
      const functionName = node.callee.name;

      // Check if this is a generic function
      if (this.genericFunctions.has(functionName)) {
        return this.instantiateGenericFunction(functionName, argTypes, node);
      }
    }

    // For non-generic functions, use your existing logic
    const calleeType = this.visitNode(node.callee);
    const returnType = this.freshTypeId();
    const expectedFunctionType = this.createFunctionType(argTypes, returnType);

    if (!this.unify(calleeType, expectedFunctionType, node)) {
      this.reportError(
        `Function call type mismatch: expected function with ${argTypes.length} parameters`,
        node
      );
    }

    return returnType;
  }

  instantiateGenericFunction(
    functionName: string,
    argTypes: number[],
    callNode: CallExpression
  ): number {
    const functionNode = this.functionNodes.get(functionName);
    if (!functionNode) {
      this.reportError(`Unknown function: ${functionName}`, callNode);
      return this.freshTypeId();
    }

    // Check if we already have this instantiation
    const existingInstantiation = this.findExistingInstantiation(
      functionName,
      argTypes
    );
    if (existingInstantiation) {
      return existingInstantiation.returnType;
    }

    // Create new instantiation
    const savedScope = { ...this.scope };

    // Bind parameters to argument types
    for (let i = 0; i < functionNode.params.length; i++) {
      const param = functionNode.params[i];
      this.scope[param.name] = argTypes[i];
      withId(param).typeId = argTypes[i];
    }

    // Type check the function body
    const returnType = this.visitNode(functionNode.body);

    // Create mangled name for C code generation
    const mangledName = this.createMangledName(functionName, argTypes);

    // Store this instantiation
    if (!this.functionInstantiations.has(functionName)) {
      this.functionInstantiations.set(functionName, []);
    }

    this.functionInstantiations.get(functionName)!.push({
      paramTypes: argTypes,
      returnType,
      mangledName,
    });

    // Restore scope
    this.scope = savedScope;

    // Store mangled name on the call node for code generation
    (callNode as any).mangledFunctionName = mangledName;

    return returnType;
  }

  findExistingInstantiation(functionName: string, argTypes: number[]) {
    const instantiations = this.functionInstantiations.get(functionName);
    if (!instantiations) return null;

    return instantiations.find(
      (inst) =>
        inst.paramTypes.length === argTypes.length &&
        inst.paramTypes.every((paramType, i) =>
          this.typesEqual(paramType, argTypes[i])
        )
    );
  }

  typesEqual(typeId1: number, typeId2: number): boolean {
    const type1 = this.resolveSymlinksAndCompress(typeId1);
    const type2 = this.resolveSymlinksAndCompress(typeId2);

    if (type1 === type2) return true;

    const entry1 = this.db[type1];
    const entry2 = this.db[type2];

    return (
      this.isConcrete(entry1) &&
      this.isConcrete(entry2) &&
      entry1.concrete === entry2.concrete
    );
  }

  createMangledName(functionName: string, argTypes: number[]): string {
    const typeNames = argTypes.map((typeId) => {
      const typeName = this.getConcreteTypeName(typeId);
      return typeName || "unknown";
    });

    return createMangledFunctionName(functionName, typeNames);
  }

  // instantiateGenericFunction(
  //   functionName: string,
  //   argTypes: number[],
  //   callNode: CallExpression,
  // ): number {
  //   const functionNode = this.functionNodes.get(functionName);
  //   if (!functionNode) {
  //     this.reportError(`Unknown function: ${functionName}`, callNode);
  //     return this.freshTypeId();
  //   }
  //
  //   // Check parameter count
  //   if (functionNode.params.length !== argTypes.length) {
  //     this.reportError(
  //       `Function ${functionName} expects ${functionNode.params.length} arguments, got ${argTypes.length}`,
  //       callNode,
  //     );
  //     return this.freshTypeId();
  //   }
  //
  //   // Create fresh type variables for this instantiation
  //   const savedScope = { ...this.scope };
  //
  //   // Bind parameters to argument types
  //   for (let i = 0; i < functionNode.params.length; i++) {
  //     const param = functionNode.params[i];
  //     this.scope[param.name] = argTypes[i];
  //     withId(param).typeId = argTypes[i];
  //   }
  //
  //   // Type check the function body with these concrete parameter types
  //   const returnType = this.visitNode(functionNode.body);
  //
  //   // Restore scope
  //   this.scope = savedScope;
  //
  //   return returnType;
  // }

  // NOTE: our language uses variable statements instead which can have multiple declarations
  // visitConstDeclaration(node) {
  //   const initType = this.visitNode(node.init);
  //
  //   // Assign type to the declared identifier
  //   node.id.typeId = initType;
  //
  //   // Add the variable to scope
  //   this.scope[node.id.name] = initType;
  //
  //   // If there's a type annotation, check it matches the initialization
  //   if (node.typeAnnotation) {
  //     // In a real implementation, process the type annotation
  //     // and unify it with initType
  //   }
  //
  //   return initType;
  // }

  visitVariableStatement(node: VariableStatement): number {
    const declarationTypes = [];

    for (const declaration of node.declarations) {
      let declType;

      if (declaration.init) {
        // Variable has an initializer - infer type from it
        declType = this.visitNode(declaration.init);
      } else {
        // No initializer - create a fresh type variable
        declType = this.freshTypeId();
      }

      // Assign type to the declared identifier
      withId(declaration.id).typeId = declType;

      // Add the variable to scope
      this.scope[declaration.id.name] = declType;

      // If there's a type annotation, unify it with the inferred/default type
      // if (declaration.typeAnnotation) {
      //   const annotationType = this.processTypeAnnotation(
      //     declaration.typeAnnotation,
      //   );
      //   try {
      //     this.unify(declType, annotationType);
      //     // Update with the unified type
      //     declType = this.substitute(declType);
      //     declaration.id.typeId = declType;
      //     this.scope[declaration.id.name] = declType;
      //   } catch (unificationError) {
      //     this.reportError(
      //       `Type annotation doesn't match inferred type for '${declaration.id.name}'`,
      //       declaration,
      //     );
      //   }
      // }

      declarationTypes.push(declType);
    }

    // Return the types of all declarations (or just void/unit type)
    return declarationTypes.length === 1
      ? declarationTypes[0]
      : this.createConcreteType("Void");
  }

  visitBlockStatement(node: BlockStatement): number {
    let lastType = this.createConcreteType("Void");

    // Visit each statement in the block
    for (const statement of node.body) {
      lastType = this.visitNode(statement);
    }

    return lastType;
  }

  visitReturnStatement(node: ReturnStatement): number {
    if (node.argument) {
      const returnType = this.visitNode(node.argument);

      // TODO: In a complete implementation, you'd need to track the current
      // function's expected return type and unify with it
      // For now, just return the argument type
      return returnType;
    }

    return this.createConcreteType("Void");
  }

  getAllFunctionInstantiations(): Map<
    string,
    Array<{
      paramTypes: number[];
      returnType: number;
      mangledName: string;
      originalNode: FunctionDeclaration;
    }>
  > {
    const result = new Map();

    for (const [functionName, instantiations] of this.functionInstantiations) {
      const originalNode = this.functionNodes.get(functionName)!;

      result.set(
        functionName,
        instantiations.map((inst) => ({
          ...inst,
          originalNode,
        }))
      );
    }

    return result;
  }

  typeCheck(statements: Statement[]) {
    // Reset globals
    this.db = [];
    this.errors = [];
    this.nextTypeId = 0;
    this.scope = {}; // Reset the scope

    // Visit each statement in the program
    for (const statement of statements) {
      this.visitNode(statement);
    }

    for (const [i, type] of this.db.entries()) {
      console.log(i, type);
    }

    return { errors: this.errors };
  }
}

function withId<T extends ASTNode>(node: T): T & { typeId: number } {
  return node as T & { typeId: number };
}
