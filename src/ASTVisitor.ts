import {
  ASTNode,
  BlockStatement,
  Expression,
  Identifier,
  Statement,
  StructProperty,
} from "./AST.ts";

export type VisitorFunction = (
  child: ASTNode,
  key: string,
  parent: ASTNode
) => void;
export type TransformFunction<T extends ASTNode = ASTNode> = (node: T) => T;

export class ASTVisitor {
  /**
   * Visit all child nodes of a given AST node
   */
  static visitChildren(node: ASTNode, visitor: VisitorFunction): void {
    switch (node.type) {
      case "Program":
      case "BlockStatement": {
        for (const stmt of node.body) {
          visitor(stmt, "body", node);
        }
        break;
      }

      case "FunctionDeclaration": {
        for (const param of node.params) {
          visitor(param, "params", node);
        }
        visitor(node.body, "body", node);
        break;
      }

      case "FunctionExpression": {
        for (const param of node.params) {
          visitor(param, "params", node);
        }
        visitor(node.body, "body", node);
        break;
      }

      case "ExpressionStatement": {
        visitor(node.expression, "expression", node);
        break;
      }

      case "VariableStatement": {
        for (const decl of node.declarations) {
          visitor(decl.id, "declarations", node);
          if (decl.init) {
            visitor(decl.init, "declarations", node);
          }
        }
        break;
      }

      case "ReturnStatement": {
        if (node.argument) {
          visitor(node.argument, "argument", node);
        }
        break;
      }

      case "CallExpression": {
        visitor(node.callee, "callee", node);
        for (const arg of node.args) {
          visitor(arg, "args", node);
        }
        break;
      }

      case "BinaryExpression": {
        visitor(node.left, "left", node);
        visitor(node.right, "right", node);
        break;
      }

      case "MemberExpression": {
        visitor(node.object, "object", node);
        visitor(node.property, "property", node);
        break;
      }

      case "StructLiteral": {
        for (const field of node.fields) {
          visitor(field, "fields", node);
        }
        break;
      }

      case "StructProperty": {
        visitor(node.name, "name", node);
        visitor(node.value, "value", node);
        break;
      }

      // Leaf nodes - no children to visit
      case "Identifier":
      case "NumericLiteral":
      case "StringLiteral":
      case "BooleanLiteral":
        break;

      default: {
        console.warn(
          `Unknown node type in ASTVisitor: ${(node as ASTNode).type}`
        );
        break;
      }
    }
  }

  /**
   * Recursively visit all nodes in the AST (depth-first)
   */
  static walkAST(node: ASTNode, visitor: VisitorFunction): void {
    this.visitChildren(node, (child, key, parent) => {
      visitor(child, key, parent);
      this.walkAST(child, visitor);
    });
  }

  /**
   * Transform an AST by applying a transformation function to each node
   */
  static transform<T extends ASTNode>(
    node: T,
    transformer: TransformFunction
  ): T {
    // First transform children
    this.visitChildren(node, (child, key, parent) => {
      const transformed = this.transform(child, transformer);
      this.setChildNode(parent, key, child, transformed);
    });

    // Then transform the current node
    return transformer(node) as T;
  }

  /**
   * Helper to set a child node in the parent (handles arrays and single properties)
   */
  private static setChildNode(
    parent: ASTNode,
    key: string,
    oldChild: ASTNode,
    newChild: ASTNode
  ): void {
    switch (parent.type) {
      case "Program":
      case "BlockStatement": {
        const bodyIndex = parent.body.indexOf(oldChild as Statement);
        if (bodyIndex !== -1) {
          parent.body[bodyIndex] = newChild as Statement;
        }
        break;
      }

      case "FunctionDeclaration": {
        if (key === "body") {
          parent.body = newChild as BlockStatement;
        } else if (key === "params") {
          const paramIndex = parent.params.indexOf(oldChild as Identifier);
          if (paramIndex !== -1) {
            parent.params[paramIndex] = newChild as Identifier;
          }
        }
        break;
      }

      case "FunctionExpression": {
        if (key === "body") {
          parent.body = newChild as BlockStatement | Expression;
        } else if (key === "params") {
          const paramIndex = parent.params.indexOf(oldChild as Identifier);
          if (paramIndex !== -1) {
            parent.params[paramIndex] = newChild as Identifier;
          }
        }
        break;
      }

      case "ExpressionStatement": {
        if (key === "expression") {
          parent.expression = newChild as Expression;
        }
        break;
      }

      case "CallExpression": {
        if (key === "callee") {
          parent.callee = newChild as Expression;
        } else if (key === "args") {
          const argIndex = parent.args.indexOf(oldChild as Expression);
          if (argIndex !== -1) {
            parent.args[argIndex] = newChild as Expression;
          }
        }
        break;
      }

      case "BinaryExpression": {
        if (key === "left") {
          parent.left = newChild as Expression;
        } else if (key === "right") {
          parent.right = newChild as Expression;
        }
        break;
      }

      case "ReturnStatement": {
        if (key === "argument") {
          parent.argument = newChild as Expression;
        }
        break;
      }

      case "VariableStatement": {
        // Handle variable declarations if needed
        for (const decl of parent.declarations) {
          if (decl.id === oldChild) {
            decl.id = newChild as Identifier;
          } else if (decl.init === oldChild) {
            decl.init = newChild as Expression;
          }
        }
        break;
      }

      case "MemberExpression": {
        if (key === "object") {
          parent.object = newChild as Expression;
        } else if (key === "property") {
          parent.property = newChild as Expression;
        }
        break;
      }

      case "StructLiteral": {
        if (key === "fields") {
          const fieldIndex = parent.fields.indexOf(oldChild as StructProperty);
          if (fieldIndex !== -1) {
            parent.fields[fieldIndex] = newChild as StructProperty;
          }
        }
        break;
      }

      case "StructProperty": {
        if (key === "name") {
          parent.name = newChild as Identifier;
        } else if (key === "value") {
          parent.value = newChild as Expression;
        }
        break;
      }

      default: {
        console.warn(`Unknown parent type in setChildNode: ${parent.type}`);
        break;
      }
    }
  }

  /**
   * Find all nodes of a specific type
   */
  static findNodes<T extends ASTNode>(root: ASTNode, nodeType: string): T[] {
    const found: T[] = [];

    if (root.type === nodeType) {
      found.push(root as T);
    }

    this.visitChildren(root, (child) => {
      found.push(...this.findNodes<T>(child, nodeType));
    });

    return found;
  }
}
