import chalk from "npm:chalk";
import stripAnsi from "npm:strip-ansi";
import {
  AssignmentExpression,
  ASTNode,
  BinaryExpression,
  BlockStatement,
  CallExpression,
  ClassDeclaration,
  Closure,
  DoWhileStatement,
  ExpressionStatement,
  ForStatement,
  FunctionDeclaration,
  FunctionExpression,
  IfStatement,
  LogicalExpression,
  MemberExpression,
  NewExpression,
  Program,
  ReturnStatement,
  Statement,
  StructLiteral,
  UnaryExpression,
  VariableDeclaration,
  VariableStatement,
  WhileStatement,
} from "../AST.ts";

function pad(depth: number) {
  return " ".repeat(depth * 2);
}

function kw(kw: string | TemplateStringsArray) {
  return chalk.magenta(kw);
}

function op(op: string | TemplateStringsArray) {
  return chalk.cyan(op);
}

function sym(sym: string | TemplateStringsArray) {
  return chalk.dim(sym);
}

function lit(lit: string | TemplateStringsArray) {
  return chalk.yellow(lit);
}

function str(str: string | TemplateStringsArray) {
  return chalk.green(str);
}

function id(id: string | TemplateStringsArray) {
  return chalk.red(id);
}

function type(type: string | TemplateStringsArray) {
  return chalk.blueBright(type);
}

interface ToStringOptions {
  semi?: boolean;
}

export class CodeGen {
  gen(node: ASTNode | null, depth = 0, options: ToStringOptions = {}): string {
    if (!node) return "";
    switch (node.type) {
      // Expressions
      case "Identifier":
        return id(node.name);
      case "NullLiteral":
        return lit("null");
      case "BooleanLiteral":
        return lit(node.value.toString());
      case "NumericLiteral":
        return lit(node.value.toString());
      case "StringLiteral":
        return str(`"${node.value}"`);
      case "StructLiteral":
        return this.StructLiteral(node, depth);
      case "UnaryExpression":
        return this.UnaryExpression(node);
      case "LogicalExpression":
        return this.BinaryExpression(node);
      case "BinaryExpression":
        return this.BinaryExpression(node);
      case "AssignmentExpression":
        return this.AssignmentExpression(node);
      case "FunctionExpression":
        return this.FunctionExpression(node, depth);
      case "CallExpression":
        return this.CallExpression(node, depth);
      case "NewExpression":
        return this.NewExpression(node, depth);
      case "MemberExpression":
        return this.MemberExpression(node, depth);
      case "ThisExpression":
        return kw`this`;
      case "SuperExpression":
        return kw`super`;
      case "Closure":
        return this.Closure(node, depth);

      // Statements
      case "NewLine":
        return "";
      case "EmptyStatement":
        return sym`;`;
      case "Comment":
        return pad(depth) + chalk.dim.italic(node.value);
      case "Program":
        return this.Program(node);
      case "BlockStatement":
        return this.Block(node, depth);
      case "ExpressionStatement":
        return this.ExpressionStatement(node, depth);
        // return pad(depth) + this.gen(node.expression, depth) + sym`;`;
      case "VariableStatement":
        return this.VariableStatement(node, depth, options);
      case "IfStatement":
        return this.IfStatement(node, depth);
      case "WhileStatement":
        return this.WhileStatement(node, depth);
      case "DoWhileStatement":
        return this.DoWhileStatement(node, depth);
      case "ForStatement":
        return this.ForStatement(node, depth);
      case "FunctionDeclaration":
        return this.FunctionDeclaration(node, depth);
      case "ReturnStatement":
        return this.ReturnStatement(node, depth);
      case "ClassDeclaration":
        return this.ClassDeclaration(node, depth);
      default:
        // deno-lint-ignore no-explicit-any
        return `<${(node as any).type || "unknown"}>`;
    }
  }

  // --- Statements ---

  Program(node: Program) {
    return node.body.map((node) => this.gen(node, 0)).join("\n");
  }

  Block(block: BlockStatement, depth = 0) {
    return this.maybeBlock(block);
    // if (!block.body.length) return pad(depth) + sym`\{\}`;
    // return [
    //   pad(depth) + sym`\{`,
    //   this.StatementList(block.body, depth + 1),
    //   pad(depth) + sym`\}`,
    // ].join("\n");
  }

  maybeBlock(node: Statement, depth = 0) {
    if (node.type !== "BlockStatement") {
      return `\n${this.gen(node, depth + 1)}`;
    }
    const stmts = this.StatementList(node.body, depth + 1);
    return ` ${sym`\{`}\n${stmts}\n${pad(depth)}${sym`\}`}`;
  }

  ExpressionStatement(node: ExpressionStatement, depth = 0) {
    return pad(depth) + this.gen(node.expression, depth) + sym`;`;
  }

  VariableStatement(
    node: VariableStatement,
    depth = 0,
    options: ToStringOptions = {},
  ): string {
    const decls = this.DeclarationList(node.declarations);
    const semi = (options.semi ?? true) ? sym`;` : "";
    if (node.isConstant) return pad(depth) + kw`let ` + decls + semi;
    return pad(depth) + kw`var ` + decls + semi;
  }

  DeclarationList(decls: VariableDeclaration[]) {
    return decls
      .map((d) => {
        let str = d.id.name;
        if (d.inferredType) {
          str += sym`: `;
          str += type(d.inferredType);
        } else if (d.init?.inferredType) {
          str += sym`: `;
          str += type(d.init.inferredType);
        }
        str += sym` = `;
        str += this.gen(d.init, 0);
        return str;
      })
      .join(", ");
  }

  StatementList(stmts: Statement[], depth = 0) {
    if (!stmts.length) return pad(depth);
    return stmts.map((node) => this.gen(node, depth)).join("\n");
  }

  IfStatement(
    node: IfStatement,
    depth = 0,
  ) {
    let str = pad(depth) + kw`if`;
    str += sym` (`;
    str += this.gen(node.test, depth);
    str += sym`)`;
    str += this.maybeBlock(node.consequent, depth);
    if (node.alternate) {
      if (stripAnsi(str).trim().at(-1) === "}") str += kw` else`;
      else str += "\n" + pad(depth) + kw`else`;
      str += this.maybeBlock(node.alternate, depth);
    }
    return str;
  }

  WhileStatement(node: WhileStatement, depth = 0) {
    let str = pad(depth) + kw`while`;
    str += sym` (`;
    str += this.gen(node.test, depth);
    str += sym`)`;
    str += this.maybeBlock(node.body, depth);
    return str;
  }

  DoWhileStatement(node: DoWhileStatement, depth = 0) {
    let str = pad(depth) + kw`do`;
    str += this.maybeBlock(node.body, depth);
    str += kw` while`;
    str += sym` (`;
    str += this.gen(node.test, depth);
    str += sym`);`;
    return str;
  }

  ForStatement(node: ForStatement, depth = 0) {
    let str = pad(depth) + kw`for`;
    str += sym` (`;
    str += node.init ? `${this.gen(node.init, 0, { semi: false })};` : ";";
    str += node.test ? ` ${this.gen(node.test, depth)};` : ";";
    str += node.update ? ` ${this.gen(node.update, depth)}` : "";
    str += sym`)`;
    str += this.maybeBlock(node.body, depth);
    return str;
  }

  FunctionDeclaration(node: FunctionDeclaration, depth = 0) {
    let str = pad(depth) + kw`fn `;
    str += id(node.name.name);
    str += sym`(`;
    str += node.params.map((p) => {
      if (!p.inferredType) return id(p.name);
      return id(p.name) + sym`: ` + type(p.inferredType);
    }).join(", ");
    str += sym`)`;
    if (node.inferredType) {
      str += sym` -> ` + type(node.inferredType);
    }
    str += this.maybeBlock(node.body, depth);
    return str;
  }

  ReturnStatement(node: ReturnStatement, depth = 0) {
    let str = pad(depth) + kw`return`;
    str += node.argument ? ` ${this.gen(node.argument, depth)}` : "";
    str += sym`;`;
    return str;
  }

  ClassDeclaration(node: ClassDeclaration, depth = 0) {
    let str = pad(depth) + kw`class `;
    str += id(node.id.name);
    str += this.maybeBlock(node.body, depth);
    return str;
  }

  // --- Expressions ---

  StructLiteral(node: StructLiteral, depth = 0) {
    let str = pad(depth) + sym`\{ `;
    str += node.fields.map((field) => {
      let s = id(field.name.name) + sym`: `;
      s += this.gen(field.value, depth);
      return s;
    }).join(", ");
    str += pad(depth) + sym` \}`;
    return str;
  }

  UnaryExpression(node: UnaryExpression) {
    const argument = this.gen(node.argument, 0);
    return `${op(node.operator)}${argument}`;
  }

  BinaryExpression(
    node: BinaryExpression | LogicalExpression,
  ) {
    const left = this.gen(node.left, 0);
    const right = this.gen(node.right, 0);
    const expr = `${left} ${op(node.operator)} ${right}`;
    if (node.parenthesized) return sym`(` + expr + sym`)`;
    return expr;
  }

  AssignmentExpression(node: AssignmentExpression) {
    const left = this.gen(node.left, 0);
    const right = this.gen(node.right, 0);
    return `${left} ${op(node.operator)} ${right}`;
  }

  FunctionExpression(node: FunctionExpression, depth = 0) {
    let str = pad(depth) + kw`fn`;
    str += sym`(`;
    str += node.params.map((p) => {
      if (!p.inferredType) return id(p.name);
      return id(p.name) + sym`: ` + type(p.inferredType);
    }).join(", ");
    str += sym`)`;
    if (node.inferredType) {
      str += sym` -> ` + type(node.inferredType);
    }
    str += " ";
    if (node.body.type === "BlockStatement") {
      str += this.Block(node.body, depth);
    } else {
      str += this.gen(node.body, depth);
    }
    return str;
  }

  CallExpression(node: CallExpression, depth = 0) {
    let str = this.gen(node.callee, depth);
    str += sym`(`;
    str += node.args.map((arg) => this.gen(arg, depth)).join(", ");
    str += sym`)`;
    return str;
  }

  MemberExpression(node: MemberExpression, depth = 0) {
    let str = this.gen(node.object, depth);
    str += node.computed ? sym`[` : sym`.`;
    str += this.gen(node.property);
    if (node.computed) str += sym`]`;
    return str;
  }

  NewExpression(node: NewExpression, depth = 0) {
    let str = pad(depth) + kw`new `;
    str += this.gen(node.callee, depth);
    str += sym`(`;
    str += node.args.map((arg) => this.gen(arg, depth)).join(", ");
    str += sym`)`;
    return str;
  }

  Closure(node: Closure, depth = 0) {
    let str = kw`Closure`;
    str += sym`(`;
    str += id(node.func.name);
    str += sym`, `;
    str += this.gen(node.environment, depth);
    str += sym`)`;
    return str;
  }
}
