import {
  ASTNode,
  BlockStatement,
  Closure,
  Expression,
  FunctionDeclaration,
  Identifier,
  Program,
  Statement,
} from "./AST.ts";

interface Scope {
  name: string;
  map: Record<string, string>;
  parent?: Scope;
}

function Scope(
  name: string,
  map: Record<string, string>,
  parent?: Scope
): Scope {
  if (!parent) return { name, map };
  return { name, map, parent };
}

export class ClosureConverter {
  private scopeStack = Array<Scope>();
  private hoistedFunctions = Array<FunctionDeclaration>();

  convert<T extends ASTNode>(node: T): T {
    // Reset state for each conversion
    this.scopeStack = [];
    this.hoistedFunctions = [];

    const result = this.transformNode(node);

    // Add hoisted functions to the program root
    if (result.type === "Program") {
      const program = result as Program;
      // Filter out any null statements (removed nested functions)
      program.body = program.body.filter(Boolean);
      // Prepend hoisted functions
      program.body = [...this.hoistedFunctions, ...program.body];
    }

    return result as T;
  }

  private transformNode(node: ASTNode): ASTNode {
    switch (node.type) {
      case "Closure":
        node.func = this.transformNode(node.func) as Identifier;
        node.environment = this.transformNode(node.environment) as Expression;
        return node;

      case "FunctionDeclaration":
        return this.transformFunctionDeclaration(node);

      case "Program":
      case "BlockStatement":
        return this.transformBlock(node);

      case "FunctionExpression":
        return this.transformFunctionExpression(node);

      case "ExpressionStatement":
        node.expression = this.transformNode(node.expression) as any;
        return node;

      case "VariableStatement":
        for (const decl of node.declarations) {
          if (decl.init) {
            decl.init = this.transformNode(decl.init) as any;
          }
        }
        return node;

      case "ReturnStatement":
        if (node.argument) {
          node.argument = this.transformNode(node.argument) as any;

          // If returning a function identifier, wrap it in a Closure
          if (
            node.argument?.type === "Identifier" &&
            this.isClosureFunction(node.argument.name)
          ) {
            node.argument = new Closure(
              node.argument,
              new Identifier("__closure_env")
            );
          }
        }
        return node;

      case "CallExpression":
        node.callee = this.transformNode(node.callee) as any;
        node.args = node.args.map((arg) => this.transformNode(arg) as any);

        // If calling a closure, inject the environment as first argument
        if (node.callee.type === "Closure") {
          const closure = node.callee as Closure;
          node.callee = closure.func; // Call the actual function
          node.args = [closure.environment, ...node.args]; // Inject environment
        }

        return node;

      case "BinaryExpression":
        node.left = this.transformNode(node.left) as any;
        node.right = this.transformNode(node.right) as any;
        return node;

      case "MemberExpression":
        node.object = this.transformNode(node.object) as any;
        node.property = this.transformNode(node.property) as any;
        return node;

      case "StructLiteral":
        for (const field of node.fields) {
          field.value = this.transformNode(field.value) as any;
        }
        return node;

      // Check if an identifier refers to a closure function
      case "Identifier":
        if (this.isClosureFunction(node.name)) {
          return new Closure(node, new Identifier("__closure_env"));
        }
        return node;

      // Leaf nodes - no transformation needed
      case "NumericLiteral":
      case "StringLiteral":
      case "BooleanLiteral":
        return node;

      default:
        console.warn(`Unknown node type in ClosureConverter: ${node.type}`);
        return node;
    }
  }

  private transformBlock(
    node: Program | BlockStatement
  ): Program | BlockStatement {
    const transformedBody: Statement[] = [];

    for (const stmt of node.body) {
      const transformed = this.transformNode(stmt) as Statement;
      // Only add non-null statements (null means the statement was hoisted)
      if (transformed !== null) {
        transformedBody.push(transformed);
      }
    }

    node.body = transformedBody;
    return node;
  }

  private transformFunctionDeclaration(
    node: FunctionDeclaration
  ): FunctionDeclaration | null {
    // Push new scope for this function
    this.scopeStack.push(Scope(node.name.name, {}));

    try {
      // Transform parameters (though they're usually just identifiers)
      node.params = node.params.map(
        (param) => this.transformNode(param) as Identifier
      );

      // Transform the function body to handle any nested functions
      node.body = this.transformNode(node.body) as BlockStatement;

      // Check if this is a nested function (not at top level)
      if (this.scopeStack.length > 1) {
        // Generate closure name from scope stack
        node.name.name =
          "__closure_" + this.scopeStack.map((s) => s.name).join("_");

        // Add closure environment parameter
        node.params = [new Identifier("__closure_env"), ...node.params];

        // Hoist this function to top level
        this.hoistedFunctions.push(node);

        // Return null to remove from current position
        return null;
      }

      // Top-level function, keep in place
      return node;
    } finally {
      // Always pop the scope when done with this function
      this.scopeStack.pop();
    }
  }

  private transformFunctionExpression(node: any): any {
    if (node.name) {
      this.scopeStack.push(Scope(node.name.name, {}));
    } else {
      // Anonymous function - generate a name
      const anonName = `anon_${this.scopeStack.length}`;
      this.scopeStack.push(Scope(anonName, {}));
    }

    try {
      // Transform parameters
      node.params = node.params.map((param: any) => this.transformNode(param));

      // Transform the function body
      if (node.body.type === "BlockStatement") {
        node.body = this.transformNode(node.body) as BlockStatement;
      } else {
        // Arrow function with expression body
        node.body = this.transformNode(node.body);
      }

      return node;
    } finally {
      this.scopeStack.pop();
    }
  }

  // Helper method to check if an identifier refers to a closure function
  private isClosureFunction(name: string): boolean {
    return name.startsWith("__closure_");
  }
}
