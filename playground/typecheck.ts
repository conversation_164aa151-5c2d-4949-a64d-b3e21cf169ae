import {
  ASTNode,
  BinaryExpression,
  BlockStatement,
  CallExpression,
  ExpressionStatement,
  FunctionDeclaration,
  FunctionExpression,
  Identifier,
  MemberExpression,
  Program,
  ReturnStatement,
  StructDeclaration,
  StructLiteral,
  StructProperty,
  VariableStatement,
} from "../src/AST.ts";
import { BuiltinInjector } from "../src/BuiltinInjector.ts";
import { ClosureConverter } from "../src/ClosureConverter.ts";
import { CodeGen } from "../src/codegen/CodeGen.ts";
import { LambdaConverter } from "../src/LambdaConverter.ts";
import { Monomorphizer } from "../src/Monomorphizer.ts";
import { NameChecker } from "../src/NameChecker.ts";
import { OperatorOverloader } from "../src/OperatorOverloader.ts";
import { Parser } from "../src/Parser.ts";
// import { <PERSON><PERSON>hecker } from "../src/TypeChecker.ts";

type FunctionType = {
  concrete: "Function";
  params: number[];
  returnType: number;
};

type FunctionValueType = {
  concrete: "FunctionValue";
  funcExpr: FunctionExpression;
  paramTypes?: number[]; // Optional - inferred parameter types
  callSites?: number[][]; // Track all call sites with their argument types
};

type StructType = {
  concrete: "Struct";
  name?: string; // Optional - can be inferred
  fields: Map<string, number>; // field name -> type id
};

type FieldAccessType = {
  concrete: "FieldAccess";
  structType: number;
  fieldName: string;
};

type UnionType = {
  concrete: "Union";
  types: number[]; // Array of type IDs that make up the union
};

type ConcreteType =
  | { concrete: string; origin?: string }
  | FunctionValueType
  | FunctionType
  | StructType
  | FieldAccessType
  | UnionType;
type SymlinkType = { symlink: number };
type TypeEntry = ConcreteType | SymlinkType;

export class TypeChecker {
  private db = Array<TypeEntry | null>();
  private scope = new Map<string, number>();
  private errors: Array<{ message: string; node: ASTNode }> = [];
  private nextTypeId = 0;

  private functions = new Map<string, FunctionDeclaration>();
  private structDefinitions = new Map<string, StructDeclaration>();

  freshTypeId() {
    const id = this.nextTypeId++;
    this.db[id] = null;
    return id;
  }

  saveScope(): Map<string, number> {
    return new Map(this.scope);
  }

  restoreScope(savedScope: Map<string, number>): void {
    this.scope = savedScope;
  }

  setVariableType(name: string, typeId: number): void {
    this.scope.set(name, typeId);
  }

  reportError(message: string, node: ASTNode) {
    this.errors.push({ message, node });
  }

  private withId<T extends ASTNode>(node: T): T & { typeId: number } {
    return node as T & { typeId: number };
  }

  private setInferredType(node: ASTNode, typeId: number) {
    const typeName = this.getConcreteTypeName(typeId);
    if (typeName) node.inferredType = typeName;
    this.withId(node).typeId = typeId;
    return typeId;
  }

  public getConcreteTypeName(typeId: number): string | null {
    const ultimateId = this.resolveSymlinksAndCompress(typeId);
    const entry = this.db[ultimateId];

    if (this.isStructType(entry)) {
      if (entry.name) {
        return entry.name;
      } else {
        return `{ ${
          Array.from(entry.fields.entries())
            .map(([fieldName, fieldTypeId]) => {
              return `${fieldName}: ${this.getConcreteTypeName(fieldTypeId)}`;
            })
            .join(", ")
        } }`;
      }
    }

    if (this.isFieldAccessType(entry)) {
      const structTypeName = this.getConcreteTypeName(entry.structType);
      return `${structTypeName}.${entry.fieldName}`;
    }

    if (this.isFunctionValue(entry)) {
      if (entry.callSites && entry.callSites.length > 0) {
        // Create union types for parameters based on all call sites
        const paramUnions: number[] = [];
        const paramCount = entry.funcExpr.params.length;

        for (let paramIndex = 0; paramIndex < paramCount; paramIndex++) {
          const paramTypes = entry.callSites
            .map((callSite) => callSite[paramIndex])
            .filter((t) => t !== undefined);
          if (paramTypes.length > 0) {
            paramUnions[paramIndex] = this.createUnionType(paramTypes);
          } else {
            paramUnions[paramIndex] = this.freshTypeId();
          }
        }

        // Show detailed parameter types with unions
        const paramTypeNames = paramUnions.map(
          (typeId) => this.getConcreteTypeName(typeId) || "unknown",
        );
        const paramList = entry.funcExpr.params
          .map((param, i) => `${param.name}: ${paramTypeNames[i]}`)
          .join(", ");
        return `fn(${paramList})`;
      } else if (entry.paramTypes) {
        // Show detailed parameter types (single call site)
        const paramTypeNames = entry.paramTypes.map(
          (typeId) => this.getConcreteTypeName(typeId) || "unknown",
        );
        const paramList = entry.funcExpr.params
          .map((param, i) => `${param.name}: ${paramTypeNames[i]}`)
          .join(", ");
        return `fn(${paramList})`;
      } else {
        // Fallback to just parameter count
        const paramCount = entry.funcExpr.params.length;
        return `Function(${paramCount})`;
      }
    }

    if (this.isUnionType(entry)) {
      const typeNames = entry.types.map(
        (typeId) => this.getConcreteTypeName(typeId) || "unknown",
      );
      return typeNames.join(" | ");
    }

    if (this.isConcrete(entry)) {
      return entry.concrete;
    }

    return null;
  }

  // ----------------------------------------------------------------
  // Type Guards

  isSymlink(entry: TypeEntry | null): entry is SymlinkType {
    return entry ? "symlink" in entry : false;
  }

  isConcrete(entry: TypeEntry | null): entry is ConcreteType {
    return entry ? "concrete" in entry : false;
  }

  isFunctionType(entry: TypeEntry | null): entry is FunctionType {
    return this.isConcrete(entry) && entry.concrete === "Function";
  }

  isFunctionValue(entry: TypeEntry | null): entry is FunctionValueType {
    return this.isConcrete(entry) && entry.concrete === "FunctionValue";
  }

  isStructType(entry: TypeEntry | null): entry is StructType {
    return this.isConcrete(entry) && entry.concrete === "Struct";
  }

  isFieldAccessType(entry: TypeEntry | null): entry is FieldAccessType {
    return this.isConcrete(entry) && entry.concrete === "FieldAccess";
  }

  isConcreteStructType(entry: StructType): boolean {
    // A struct type is considered concrete if it has fields defined
    // This helps distinguish between gradual struct types and concrete ones
    return entry.fields.size > 0;
  }

  isUnionType(entry: TypeEntry | null): entry is UnionType {
    return this.isConcrete(entry) && entry.concrete === "Union";
  }

  createUnionType(types: number[]): number {
    // Remove duplicates and flatten nested unions
    const uniqueTypes = new Set<number>();
    for (const typeId of types) {
      const resolved = this.resolveSymlinksAndCompress(typeId);
      const entry = this.db[resolved];
      if (this.isUnionType(entry)) {
        // Flatten nested unions
        for (const nestedType of entry.types) {
          uniqueTypes.add(nestedType);
        }
      } else {
        uniqueTypes.add(resolved);
      }
    }

    const uniqueTypesArray = Array.from(uniqueTypes);

    // If only one type, return it directly (no need for union)
    if (uniqueTypesArray.length === 1) {
      return uniqueTypesArray[0];
    }

    // Create union type
    const id = this.nextTypeId++;
    this.db[id] = {
      concrete: "Union",
      types: uniqueTypesArray,
    };
    return id;
  }

  // ----------------------------------------------------------------
  // Type Constructors

  public createConcreteType(typeName: string, node?: ASTNode) {
    // Check if this is a struct type string and parse it properly
    if (typeName.startsWith("{") && typeName.endsWith("}")) {
      return this.parseStructTypeString(typeName);
    }

    const id = this.nextTypeId++;
    this.db[id] = { concrete: typeName };
    if (node) this.db[id].origin = node.type;
    return id;
  }

  private parseStructTypeString(typeString: string): number {
    // Parse "{ a: String, b: Number, c: Number }" into a proper StructType
    const inner = typeString.slice(1, -1).trim();
    if (inner === "") {
      return this.createStructType(new Map());
    }

    const fieldTypes = new Map<string, number>();
    const fields = inner.split(",");

    for (const field of fields) {
      const trimmed = field.trim();
      const colonIndex = trimmed.indexOf(":");
      if (colonIndex !== -1) {
        const fieldName = trimmed.substring(0, colonIndex).trim();
        const fieldTypeName = trimmed.substring(colonIndex + 1).trim();
        const fieldTypeId = this.createConcreteType(fieldTypeName);
        fieldTypes.set(fieldName, fieldTypeId);
      }
    }

    return this.createStructType(fieldTypes);
  }

  createFunctionType(paramCount: number): number {
    const id = this.nextTypeId++;
    const paramTypes = Array.from(
      { length: paramCount },
      () => this.freshTypeId(),
    );
    const returnType = this.freshTypeId();

    this.db[id] = {
      concrete: "Function",
      params: paramTypes,
      returnType: returnType,
    };

    return id;
  }

  createStructType(fields: Map<string, number>, name?: string): number {
    const id = this.nextTypeId++;
    this.db[id] = {
      concrete: "Struct",
      name,
      fields: new Map(fields),
    };
    return id;
  }

  createFieldAccessType(structType: number, fieldName: string): number {
    const id = this.nextTypeId++;
    this.db[id] = {
      concrete: "FieldAccess",
      structType,
      fieldName,
    };
    return id;
  }

  // ----------------------------------------------------------------
  // Core algorithms

  reportTypeMismatch(typeId1: number, typeId2: number, node: ASTNode) {
    const type1Name = this.getConcreteTypeName(typeId1) || "unknown";
    const type2Name = this.getConcreteTypeName(typeId2) || "unknown";

    this.reportError(
      `Type mismatch: cannot unify ${type1Name} with ${type2Name}`,
      node,
    );
    return false;
  }

  unify(aTypeId: number, bTypeId: number, node: ASTNode): boolean {
    const aType = this.resolveSymlinksAndCompress(aTypeId);
    const bType = this.resolveSymlinksAndCompress(bTypeId);

    // If they're already the same, we're done
    if (aType === bType) return true;

    const aEntry = this.db[aType];
    const bEntry = this.db[bType];

    // Handle struct type unification with structural typing
    if (this.isStructType(aEntry) && this.isStructType(bEntry)) {
      // Check if all fields in both structs are compatible
      const allFields = new Set([
        ...aEntry.fields.keys(),
        ...bEntry.fields.keys(),
      ]);

      for (const fieldName of allFields) {
        const aFieldType = aEntry.fields.get(fieldName);
        const bFieldType = bEntry.fields.get(fieldName);

        if (aFieldType && bFieldType) {
          // Both have the field - unify their types
          if (!this.unify(aFieldType, bFieldType, node)) {
            return false;
          }
        } else if (aFieldType && !bFieldType) {
          // Only a has the field - add it to b
          bEntry.fields.set(fieldName, aFieldType);
        } else if (!aFieldType && bFieldType) {
          // Only b has the field - add it to a
          aEntry.fields.set(fieldName, bFieldType);
        }
      }

      // Make b point to a
      this.db[bType] = { symlink: aType };
      return true;
    }

    // If one is a struct and the other is a type variable, unify
    if (this.isStructType(aEntry) && bEntry === null) {
      this.db[bType] = { symlink: aType };
      return true;
    }

    if (this.isStructType(bEntry) && aEntry === null) {
      this.db[aType] = { symlink: bType };
      return true;
    }

    // Handle function type unification
    if (this.isFunctionType(aEntry) && this.isFunctionType(bEntry)) {
      if (aEntry.params.length !== bEntry.params.length) {
        return this.reportTypeMismatch(aType, bType, node);
      }

      // Unify parameter types
      for (let i = 0; i < aEntry.params.length; i++) {
        if (!this.unify(aEntry.params[i], bEntry.params[i], node)) {
          return false;
        }
      }

      // Unify return types
      return this.unify(aEntry.returnType, bEntry.returnType, node);
    }

    // If both have concrete types and they're different, report mismatch
    if (
      this.isConcrete(aEntry) &&
      this.isConcrete(bEntry) &&
      aEntry.concrete !== bEntry.concrete
    ) {
      return this.reportTypeMismatch(aType, bType, node);
    }

    if (aEntry === null) {
      // If aEntry is null (unassigned type variable)
      this.db[aType] = bEntry === null
        ? { symlink: bType }
        : this.isConcrete(bEntry)
        ? { concrete: bEntry.concrete }
        : { symlink: bType };
      return true;
    } else if (bEntry === null) {
      return this.unify(bTypeId, aTypeId, node); // Swap the args
    } else if (this.isConcrete(bEntry)) {
      // a is concrete, b is not null but must be a variable
      this.db[bType] = { symlink: aType };
    } else {
      // a is not concrete and not null, so must be a variable
      this.db[aType] = { symlink: bType };
    }

    return true;
  }

  resolveSymlinksAndCompress(typeId: number): number {
    const entry = this.db[typeId];

    // If it's null, it's a type variable that hasn't been unified yet
    if (entry === null) {
      return typeId;
    }

    // If it's a symlink, follow it (with path compression)
    if (this.isSymlink(entry)) {
      const ultimateTypeId = this.resolveSymlinksAndCompress(entry.symlink);

      // Path compression: update the symlink to point directly to the ultimate type
      if (ultimateTypeId !== entry.symlink) {
        this.db[typeId] = { symlink: ultimateTypeId };
      }

      return ultimateTypeId;
    }

    // For concrete types
    return typeId;
  }

  // ----------------------------------------------------------------
  // Statements

  visitBlock(node: BlockStatement | Program) {
    let lastType = this.createConcreteType("Void", node);

    for (const statement of node.body) {
      lastType = this.visitNode(statement);
    }

    this.setInferredType(node, lastType);
    return lastType;
  }

  visitExpressionStatement(node: ExpressionStatement) {
    const typeId = this.visitNode(node.expression);
    this.setInferredType(node, typeId);
    return typeId;
  }

  visitVariableStatement(node: VariableStatement) {
    let lastType = this.createConcreteType("Void", node);

    for (const declaration of node.declarations) {
      if (!declaration.init) throw new Error("Missing initializer");

      const initType = this.visitNode(declaration.init);

      // Set type info for the identifier
      this.setInferredType(declaration.id, initType);

      // Set type info for the declaration itself
      this.setInferredType(declaration, initType);

      this.scope.set(declaration.id.name, initType);

      lastType = initType;
    }

    this.setInferredType(node, lastType);
    return lastType;
  }

  visitFunctionDeclaration(node: FunctionDeclaration) {
    // Just store the function - don't type check yet
    this.functions.set(node.name.name, node);

    const voidType = this.createConcreteType("Void", node);
    this.setInferredType(node, voidType);
    return voidType;
  }

  visitReturnStatement(node: ReturnStatement) {
    const returnType = node.argument
      ? this.visitNode(node.argument)
      : this.createConcreteType("Void", node);
    this.setInferredType(node, returnType);
    return returnType;
  }

  // visitStructDeclaration(node: StructDeclaration): number {
  //   this.registerStructDefinition(node);
  //
  //   // Create a named struct type from the declaration
  //   const fieldTypes = new Map<string, number>();
  //
  //   for (const field of node.fields) {
  //     // For struct declarations, we create fresh type variables for each field
  //     // The actual types will be inferred when the struct is used
  //     const fieldType = this.freshTypeId();
  //     fieldTypes.set(field.name.name, fieldType);
  //   }
  //
  //   const structType = this.createStructType(fieldTypes, node.name.name);
  //
  //   // Register the struct type in scope so it can be referenced
  //   this.scope.set(node.name.name, structType);
  //
  //   const voidType = this.createConcreteType("Void", node);
  //   return this.setInferredType(node, voidType);
  // }

  visitStructDeclaration(node: StructDeclaration): number {
    // Just register the struct definition for later use
    this.registerStructDefinition(node);

    // Don't create or register the struct type yet - wait until it's used
    const voidType = this.createConcreteType("Void", node);
    this.setInferredType(node, voidType);
    return voidType;
  }

  visitStructProperty(node: StructProperty): number {
    // Visit the property name (identifier)
    const nameType = this.visitNode(node.name);

    // Visit the property value
    const valueType = this.visitNode(node.value);

    // The type of a struct property is the type of its value
    this.setInferredType(node.name, valueType);

    // Also set the inferred type for the name identifier
    // Property names in struct literals are just identifiers, so they don't have a meaningful type
    // But we can set it to indicate it's a property name
    node.name.inferredType = "PropertyName";

    return valueType;
  }

  // ----------------------------------------------------------------
  // Expressions

  visitIdentifier(node: Identifier): number {
    // Look up the variable in the scope first
    if (this.scope.has(node.name)) {
      const typeId = this.scope.get(node.name)!;
      return this.setInferredType(node, typeId);
    }

    // Check if it's a struct name (constructor reference)
    if (this.structDefinitions.has(node.name)) {
      // Create a function type for the struct constructor
      const structDecl = this.structDefinitions.get(node.name)!;
      const constructorType = this.createStructConstructorType(structDecl);
      return this.setInferredType(node, constructorType);
    }

    // Create fresh type variable for unknown identifiers
    if (this.withId(node).typeId === undefined) {
      this.withId(node).typeId = this.freshTypeId();
    }
    return this.withId(node).typeId;
  }

  private createStructConstructorType(structDecl: StructDeclaration): number {
    const paramTypes = structDecl.fields.map(() => this.freshTypeId());

    // Create the return type (the struct itself)
    const fieldTypes = new Map<string, number>();
    for (let i = 0; i < structDecl.fields.length; i++) {
      const fieldName = structDecl.fields[i].name.name;
      fieldTypes.set(fieldName, paramTypes[i]);
    }

    const returnType = this.createStructType(fieldTypes, structDecl.name.name);

    // Create function type
    const id = this.nextTypeId++;
    this.db[id] = {
      concrete: "Function",
      params: paramTypes,
      returnType: returnType,
    };

    return id;
  }

  visitBinaryExpression(node: BinaryExpression) {
    const leftType = this.visitNode(node.left);
    const rightType = this.visitNode(node.right);

    const leftConcrete = this.getConcreteTypeName(leftType);
    const rightConcrete = this.getConcreteTypeName(rightType);

    if (leftType === null || rightType === null) {
      throw new Error("Binary expression has null type");
      // return null;
    }

    if (leftConcrete && rightConcrete && leftConcrete !== rightConcrete) {
      this.reportError(
        `Type mismatch in binary operation: operands must have the same type, got ${leftConcrete} and ${rightConcrete}`,
        node,
      );
    } else if (!this.unify(leftType, rightType, node)) {
      this.reportError(
        `Type mismatch in binary operation: operands must have the same type`,
        node,
      );
    }

    node.left.inferredType = leftConcrete ?? undefined;
    node.right.inferredType = rightConcrete ?? undefined;

    this.setInferredType(node, leftType);
    return leftType;
  }

  visitFunctionExpression(node: FunctionExpression) {
    // For function expressions, we need to create a callable value
    // We'll store the function AST and create a special "function value" type

    const functionValueType = this.createFunctionValueType(node);
    this.setInferredType(node, functionValueType);
    return functionValueType;
  }

  createFunctionValueType(funcExpr: FunctionExpression): number {
    const id = this.nextTypeId++;
    this.db[id] = {
      concrete: "FunctionValue",
      funcExpr: funcExpr, // Store the AST for later use
    };
    // console.log(`Created function value type ${id} for function expression`);
    return id;
  }

  visitCallExpression(node: CallExpression) {
    const argTypes = node.args.map((arg) => this.visitNode(arg));

    // Handle direct function calls (identifiers)
    if (node.callee.type === "Identifier") {
      const functionName = node.callee.name;

      // Check if it's a struct constructor
      if (this.structDefinitions.has(functionName)) {
        return this.callStructConstructor(
          this.structDefinitions.get(functionName)!,
          argTypes,
          node,
        );
      }

      // Check if it's a declared function
      // if (this.functions.has(functionName)) {
      //   return this.callDeclaredFunction(
      //     this.functions.get(functionName)!,
      //     argTypes,
      //     node,
      //   );
      // }

      // Check if it's a variable holding a function value
      if (this.scope.has(functionName)) {
        const varTypeId = this.scope.get(functionName)!;
        const resolvedType = this.resolveSymlinksAndCompress(varTypeId);
        const entry = this.db[resolvedType];

        if (this.isFunctionValue(entry)) {
          return this.callFunctionExpression(entry.funcExpr, argTypes, node);
        }
      }
    }

    // Handle direct function expression calls: (fn(a, b) a + b)(3, 2)
    if (node.callee.type === "FunctionExpression") {
      return this.callFunctionExpression(node.callee, argTypes, node);
    }

    // Handle other expression calls (variables, etc.)
    const calleeType = this.visitNode(node.callee);
    const resolvedCalleeType = this.resolveSymlinksAndCompress(calleeType);
    const calleeEntry = this.db[resolvedCalleeType];

    if (this.isFunctionValue(calleeEntry)) {
      return this.callFunctionExpression(calleeEntry.funcExpr, argTypes, node);
    }

    // Error: trying to call something that's not a function
    this.reportError("Cannot call non-function value", node);
    return this.freshTypeId();
  }

  visitStructLiteral(node: StructLiteral): number {
    const fieldTypes = new Map<string, number>();

    for (const field of node.fields) {
      // Visit the struct property node (this will handle both name and value)
      const fieldType = this.visitNode(field.value);
      fieldTypes.set(field.name.name, fieldType);
      this.setInferredType(field, fieldType);
    }

    // Check if this matches any known struct definition
    let matchingStructName: string | undefined;
    for (const [structName, structDecl] of this.structDefinitions) {
      if (this.structLiteralMatchesDeclaration(node, structDecl)) {
        matchingStructName = structName;
        break;
      }
    }

    const structType = this.createStructType(fieldTypes, matchingStructName);
    this.setInferredType(node, structType);
    return structType;
  }

  private structLiteralMatchesDeclaration(
    literal: StructLiteral,
    decl: StructDeclaration,
  ): boolean {
    if (literal.fields.length !== decl.fields.length) {
      return false;
    }

    const literalFieldNames = new Set(literal.fields.map((f) => f.name.name));
    const declFieldNames = new Set(decl.fields.map((f) => f.name.name));

    return (
      literalFieldNames.size === declFieldNames.size &&
      [...literalFieldNames].every((name) => declFieldNames.has(name))
    );
  }

  visitMemberExpression(node: MemberExpression): number {
    const objectType = this.visitNode(node.object);

    const resolvedObjectType = this.resolveSymlinksAndCompress(objectType);
    const objectEntry = this.db[resolvedObjectType];

    // For non-computed access (obj.prop), property should be an identifier
    if (!node.computed) {
      if (node.property.type !== "Identifier") {
        this.reportError(
          "Non-computed property access requires identifier",
          node,
        );
        return this.freshTypeId();
      }

      const propertyName = (node.property as Identifier).name;
      return this.handleStructFieldAccess(
        objectType,
        objectEntry,
        propertyName,
        node,
      );
    } // For computed access (obj[key]), we need different handling
    else {
      // This could be array access, map access, etc.
      // For now, just return a fresh type variable
      this.reportError("Computed property access not yet supported", node);
      return this.freshTypeId();
    }
  }

  private handleStructFieldAccess(
    objectType: number,
    objectEntry: TypeEntry | null,
    propertyName: string,
    node: MemberExpression,
  ): number {
    if (objectEntry === null) {
      // Object is a type variable - create a struct constraint
      const fieldType = this.freshTypeId();
      const structFields = new Map([[propertyName, fieldType]]);
      const structType = this.createStructType(structFields);

      // Unify the object type with our new struct type
      if (!this.unify(objectType, structType, node)) {
        return this.freshTypeId();
      }

      return this.setInferredType(node, fieldType);
    }

    if (!this.isStructType(objectEntry)) {
      this.reportError(
        `Cannot access field '${propertyName}' on non-struct type`,
        node,
      );
      return this.freshTypeId();
    }

    let fieldType = objectEntry.fields.get(propertyName);
    if (!fieldType) {
      // Check if this is a concrete struct with a known set of fields
      if (objectEntry.name || this.isConcreteStructType(objectEntry)) {
        // This is a concrete struct - report error for missing field
        const availableFields = Array.from(objectEntry.fields.keys()).join(
          ", ",
        );
        this.reportError(
          `Field '${propertyName}' does not exist on struct type. Available fields: ${availableFields}`,
          node,
        );
        return this.freshTypeId();
      } else {
        // This is a gradual struct type - add the field as a fresh type variable
        fieldType = this.freshTypeId();
        objectEntry.fields.set(propertyName, fieldType);
      }
    }

    return this.setInferredType(node, fieldType);
  }

  // ----------------------------------------------------------------
  // Helper methods

  private callStructConstructor(
    structDecl: StructDeclaration,
    argTypes: number[],
    node: CallExpression,
  ): number {
    if (structDecl.fields.length !== argTypes.length) {
      this.reportError(
        `Struct ${structDecl.name.name} expects ${structDecl.fields.length} arguments, got ${argTypes.length}`,
        node,
      );
      return this.freshTypeId();
    }

    // Create field map with the provided argument types
    const fieldTypes = new Map<string, number>();
    for (let i = 0; i < structDecl.fields.length; i++) {
      const fieldName = structDecl.fields[i].name.name;
      fieldTypes.set(fieldName, argTypes[i]);
    }

    // Create the struct type with concrete field types
    const structType = this.createStructType(fieldTypes, structDecl.name.name);
    this.setInferredType(node, structType);
    return structType;
  }

  // private callDeclaredFunction(
  //   funcDecl: FunctionDeclaration,
  //   argTypes: number[],
  //   node: CallExpression,
  // ): number {
  //   if (funcDecl.params.length !== argTypes.length) {
  //     this.reportError(
  //       `Function expects ${funcDecl.params.length} arguments, got ${argTypes.length}`,
  //       node,
  //     );
  //     return this.freshTypeId();
  //   }
  //
  //   const savedScope = new Map(this.scope);
  //
  //   // Bind parameters to argument types
  //   for (let i = 0; i < funcDecl.params.length; i++) {
  //     this.scope.set(funcDecl.params[i].name, argTypes[i]);
  //   }
  //
  //   const returnType = this.visitNode(funcDecl.body);
  //   this.scope = savedScope;
  //
  //   return returnType;
  // }

  private callFunctionExpression(
    funcExpr: FunctionExpression,
    argTypes: number[],
    node: CallExpression,
  ): number {
    if (funcExpr.params.length !== argTypes.length) {
      this.reportError(
        `Function expects ${funcExpr.params.length} arguments, got ${argTypes.length}`,
        node,
      );
      return this.freshTypeId();
    }

    // Update the function value type with parameter types if it exists
    if (this.withId(funcExpr).typeId !== undefined) {
      const funcTypeId = this.withId(funcExpr).typeId;
      const resolvedTypeId = this.resolveSymlinksAndCompress(funcTypeId);
      const entry = this.db[resolvedTypeId];

      if (this.isFunctionValue(entry)) {
        // Track this call site
        if (!entry.callSites) {
          entry.callSites = [];
        }
        entry.callSites.push([...argTypes]);
        // console.log(
        //   `Added call site to function ${resolvedTypeId}: [${argTypes
        //     .map((t) => this.getConcreteTypeName(t))
        //     .join(", ")}]`
        // );

        // Store the parameter types for display purposes (use the first call site)
        if (!entry.paramTypes) {
          entry.paramTypes = [...argTypes];
        }
      }
    }

    const savedScope = new Map(this.scope);

    // Bind parameters to argument types
    for (let i = 0; i < funcExpr.params.length; i++) {
      this.scope.set(funcExpr.params[i].name, argTypes[i]);
    }

    const returnType = this.visitNode(funcExpr.body);
    this.scope = savedScope;

    return returnType;
  }

  private registerStructDefinition(structDecl: StructDeclaration): void {
    this.structDefinitions.set(structDecl.name.name, structDecl);
  }

  // ----------------------------------------------------------------
  // Entry

  public visitNode(node: ASTNode): number {
    switch (node.type) {
      // Statements
      case "Program":
      case "BlockStatement":
        return this.visitBlock(node);

      case "ExpressionStatement":
        return this.visitExpressionStatement(node);

      case "VariableStatement":
        return this.visitVariableStatement(node);

      case "FunctionDeclaration":
        return this.visitFunctionDeclaration(node);

      case "ReturnStatement":
        return this.visitReturnStatement(node);

      case "StructDeclaration":
        return this.visitStructDeclaration(node);

      case "StructProperty":
        return this.visitStructProperty(node);

      // Expressions
      case "Identifier":
        return this.visitIdentifier(node);

      case "BinaryExpression":
        return this.visitBinaryExpression(node);

      case "FunctionExpression":
        return this.visitFunctionExpression(node);

      case "CallExpression":
        return this.visitCallExpression(node);

      case "MemberExpression":
        return this.visitMemberExpression(node);

      // Literals
      case "StructLiteral":
        return this.visitStructLiteral(node);

      case "BooleanLiteral": {
        const typeId = this.createConcreteType("Boolean");
        return this.setInferredType(node, typeId);
      }

      case "NumericLiteral": {
        const typeId = this.createConcreteType("Number");
        return this.setInferredType(node, typeId);
      }

      case "StringLiteral": {
        const typeId = this.createConcreteType("String");
        return this.setInferredType(node, typeId);
      }

      default:
        throw new Error(`Unknown node type: ${node.type}`);
    }
  }

  public typeCheck(node: ASTNode) {
    const result = this.getConcreteTypeName(this.visitNode(node));
    return { result, errors: this.errors };
  }

  public debugFunctionTypes() {
    let foundFunctions = 0;
    for (let i = 0; i < this.db.length; i++) {
      const entry = this.db[i];
      if (entry && this.isFunctionValue(entry)) {
        foundFunctions++;
        const typeName = this.getConcreteTypeName(i);
        console.log(`${typeName}`);
      }
    }
    if (foundFunctions === 0) {
      console.log("No functions with inferred types found.");
    }
  }

  public updateFunctionParameterTypes() {
    // Update function parameter nodes with inferred union types
    for (let i = 0; i < this.db.length; i++) {
      const entry = this.db[i];
      if (
        entry &&
        this.isFunctionValue(entry) &&
        entry.callSites &&
        entry.callSites.length > 0
      ) {
        const paramCount = entry.funcExpr.params.length;

        for (let paramIndex = 0; paramIndex < paramCount; paramIndex++) {
          const paramTypes = entry.callSites
            .map((callSite) => callSite[paramIndex])
            .filter((t) => t !== undefined);

          if (paramTypes.length > 0) {
            const unionTypeId = this.createUnionType(paramTypes);
            const unionTypeName = this.getConcreteTypeName(unionTypeId);
            if (unionTypeName) {
              entry.funcExpr.params[paramIndex].inferredType = unionTypeName;
            }
          }
        }
      }
    }
  }
}

const p = new Parser();
const nc = new NameChecker();
const tc = new TypeChecker();
const oo = new OperatorOverloader(); // New pass
const bi = new BuiltinInjector(oo); // New pass
const lc = new LambdaConverter();
const cc = new ClosureConverter();
const mp = new Monomorphizer(tc);
const cg = new CodeGen();

function main() {
  const source = `

// Test struct literals vs block statements
let a = 42;                  // variable assignment
{ a };                    // struct literal as statement
{ a: 1, b: "hello" };        // struct literal with multiple fields
{};                          // empty struct literal
{ x: { y: 2 } };            // nested struct literal

// Block statements
{
  let x = 1;
  x + 2;
}

// Variable assignment with struct literal
let obj = { name: "test", value: 42 };

// Function with struct literal parameter
let foo = fn(s) s.name;
foo({ name: "hello", id: 123 });

`;

  let program = p.parse(source);

  console.group("Source Code");
  console.log(cg.gen(program));
  console.groupEnd();

  console.log("-".repeat(40));

  const { errors: nameErrors } = nc.nameCheck(program.body);
  if (nameErrors.length) {
    printErrors("❌ Name Errors:\n", nameErrors);
  }

  const { errors: typeErrors } = tc.typeCheck(program);
  if (typeErrors.length) {
    printErrors("❌ Type Errors:\n", typeErrors);
  }

  // Update function parameter types with inferred union types
  tc.updateFunctionParameterTypes();

  program = lc.convert(program);
  program = cc.convert(program);
  // program = mp.convert(program);
  program = bi.injectBuiltins(program);

  console.group("Result Code");
  console.log(cg.gen(program));
  // console.log(new CCodeGen().gen(program));
  console.groupEnd();

  // console.dir(program, { depth: null });
}

try {
  main();
} catch (e) {
  console.error(e);
}

function printErrors(label = "", errors: Array<{ message: string }>) {
  const msg = errors.map((e) => "   " + e.message).join("\n");
  throw label + msg;
}
